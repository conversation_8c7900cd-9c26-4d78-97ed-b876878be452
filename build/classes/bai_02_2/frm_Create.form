<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.ButtonGroup" name="buttonGroup1">
    </Component>
    <Component class="javax.swing.ButtonGroup" name="buttonGroup2">
    </Component>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel1" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" attributes="0">
                              <EmptySpace min="-2" pref="62" max="-2" attributes="0"/>
                              <Component id="rb_nvbc" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="82" max="-2" attributes="0"/>
                              <Component id="rb_nvhd" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" attributes="0">
                              <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                              <Component id="txt_name" max="32767" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel7" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="65" max="-2" attributes="0"/>
                      <Component id="rb_nganhan" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="separate" max="-2" attributes="0"/>
                      <Component id="rb_daihan" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="1" attributes="0">
                          <Component id="btn_them" min="-2" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <Group type="103" groupAlignment="0" max="-2" attributes="0">
                          <Group type="102" attributes="0">
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="txt_hesoluong" alignment="1" min="-2" pref="231" max="-2" attributes="0"/>
                                  <Group type="103" alignment="1" groupAlignment="0" max="-2" attributes="0">
                                      <Component id="txt_namcongtac" max="32767" attributes="0"/>
                                      <Component id="txt_luongcanban" max="32767" attributes="0"/>
                                      <Component id="txt_luonghopdong" alignment="0" min="-2" pref="231" max="-2" attributes="0"/>
                                  </Group>
                              </Group>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <EmptySpace max="32767" attributes="0"/>
                              <Component id="btn_huy" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="txt_name" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="rb_nvbc" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="rb_nvhd" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_hesoluong" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_namcongtac" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_luongcanban" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="6" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txt_luonghopdong" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel7" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="rb_nganhan" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="rb_daihan" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="btn_them" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="btn_huy" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace pref="12" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Helvetica Neue" size="24" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Th&#xea;m nh&#xe2;n vi&#xea;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="T&#xea;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_name">
    </Component>
    <Component class="javax.swing.JCheckBox" name="rb_nvbc">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="NVBC"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="rb_nvbcActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JCheckBox" name="rb_nvhd">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="NVHD"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="rb_nvhdActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="H&#x1ec7; s&#x1ed1; l&#x1b0;&#x1a1;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="N&#x103;m c&#xf4;ng t&#xe1;c"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="L&#x1b0;&#x1a1;ng c&#x103;n b&#x1ea3;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="text" type="java.lang.String" value="L&#x1b0;&#x1a1;ng h&#x1ee3;p &#x111;&#x1ed3;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel7">
      <Properties>
        <Property name="text" type="java.lang.String" value="Lo&#x1ea1;i h&#x1ee3;p &#x111;&#x1ed3;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txt_hesoluong">
    </Component>
    <Component class="javax.swing.JTextField" name="txt_namcongtac">
    </Component>
    <Component class="javax.swing.JTextField" name="txt_luongcanban">
    </Component>
    <Component class="javax.swing.JTextField" name="txt_luonghopdong">
    </Component>
    <Component class="javax.swing.JCheckBox" name="rb_nganhan">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup2"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Ng&#x1eaf;n h&#x1ea1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="rb_daihan">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup2"/>
        </Property>
        <Property name="text" type="java.lang.String" value="D&#xe0;i h&#x1ea1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btn_them">
      <Properties>
        <Property name="text" type="java.lang.String" value="Th&#xea;m"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_themActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btn_huy">
      <Properties>
        <Property name="text" type="java.lang.String" value="Hu&#x1ef7;"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btn_huyActionPerformed"/>
      </Events>
    </Component>
  </SubComponents>
</Form>

<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Menu class="javax.swing.JMenuBar" name="jMenuBar1">
      <SubComponents>
        <Menu class="javax.swing.JMenu" name="jMenu1">
          <Properties>
            <Property name="text" type="java.lang.String" value="System"/>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="menu_Exit">
              <Properties>
                <Property name="text" type="java.lang.String" value="Exit"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="menu_ExitActionPerformed"/>
              </Events>
            </MenuItem>
          </SubComponents>
        </Menu>
        <Menu class="javax.swing.JMenu" name="jMenu2">
          <Properties>
            <Property name="text" type="java.lang.String" value="Employee "/>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="menu_Create">
              <Properties>
                <Property name="text" type="java.lang.String" value="Create new"/>
              </Properties>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="menu_Management">
              <Properties>
                <Property name="text" type="java.lang.String" value="Management"/>
              </Properties>
            </MenuItem>
          </SubComponents>
        </Menu>
      </SubComponents>
    </Menu>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="menuBar" type="java.lang.String" value="jMenuBar1"/>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <EmptySpace min="0" pref="400" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <EmptySpace min="0" pref="276" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
  </Layout>
</Form>

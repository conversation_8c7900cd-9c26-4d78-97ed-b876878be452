/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JFrame.java to edit this template
 */
package bai_02_2;

import javax.swing.JOptionPane;

/**
 *
 * <AUTHOR>
 */
public class frm_Create extends javax.swing.JFrame {

    /**
     * Creates new form frm_Create
     */
    public frm_Create() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        buttonGroup2 = new javax.swing.ButtonGroup();
        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        txt_name = new javax.swing.JTextField();
        rb_nvbc = new javax.swing.JCheckBox();
        rb_nvhd = new javax.swing.JCheckBox();
        jLabel3 = new javax.swing.JLabel();
        jLabel4 = new javax.swing.JLabel();
        jLabel5 = new javax.swing.JLabel();
        jLabel6 = new javax.swing.JLabel();
        jLabel7 = new javax.swing.JLabel();
        txt_hesoluong = new javax.swing.JTextField();
        txt_namcongtac = new javax.swing.JTextField();
        txt_luongcanban = new javax.swing.JTextField();
        txt_luonghopdong = new javax.swing.JTextField();
        rb_nganhan = new javax.swing.JCheckBox();
        rb_daihan = new javax.swing.JCheckBox();
        btn_them = new javax.swing.JButton();
        btn_huy = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        jLabel1.setFont(new java.awt.Font("Helvetica Neue", 1, 24)); // NOI18N
        jLabel1.setText("Thêm nhân viên");

        jLabel2.setText("Tên");

        buttonGroup1.add(rb_nvbc);
        rb_nvbc.setText("NVBC");
        rb_nvbc.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                rb_nvbcActionPerformed(evt);
            }
        });

        buttonGroup1.add(rb_nvhd);
        rb_nvhd.setText("NVHD");
        rb_nvhd.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                rb_nvhdActionPerformed(evt);
            }
        });

        jLabel3.setText("Hệ số lương");

        jLabel4.setText("Năm công tác");

        jLabel5.setText("Lương căn bản");

        jLabel6.setText("Lương hợp đồng");

        jLabel7.setText("Loại hợp đồng");

        buttonGroup2.add(rb_nganhan);
        rb_nganhan.setText("Ngắn hạn");

        buttonGroup2.add(rb_daihan);
        rb_daihan.setText("Dài hạn");

        btn_them.setText("Thêm");
        btn_them.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btn_themActionPerformed(evt);
            }
        });

        btn_huy.setText("Huỷ");
        btn_huy.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btn_huyActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel1)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(jLabel2)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addGap(62, 62, 62)
                                .addComponent(rb_nvbc)
                                .addGap(82, 82, 82)
                                .addComponent(rb_nvhd)
                                .addGap(0, 0, Short.MAX_VALUE))
                            .addGroup(layout.createSequentialGroup()
                                .addGap(26, 26, 26)
                                .addComponent(txt_name))))
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(jLabel7)
                        .addGap(65, 65, 65)
                        .addComponent(rb_nganhan)
                        .addGap(18, 18, 18)
                        .addComponent(rb_daihan))
                    .addGroup(layout.createSequentialGroup()
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                            .addComponent(btn_them)
                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                .addComponent(jLabel3)
                                .addComponent(jLabel4)
                                .addComponent(jLabel5)
                                .addComponent(jLabel6)))
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                            .addGroup(layout.createSequentialGroup()
                                .addGap(18, 18, 18)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(txt_hesoluong, javax.swing.GroupLayout.Alignment.TRAILING, javax.swing.GroupLayout.PREFERRED_SIZE, 231, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                                        .addComponent(txt_namcongtac)
                                        .addComponent(txt_luongcanban)
                                        .addComponent(txt_luonghopdong, javax.swing.GroupLayout.PREFERRED_SIZE, 231, javax.swing.GroupLayout.PREFERRED_SIZE))))
                            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                .addComponent(btn_huy)
                                .addGap(36, 36, 36)))))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(jLabel1)
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel2)
                    .addComponent(txt_name, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(rb_nvbc)
                    .addComponent(rb_nvhd))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txt_hesoluong, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel3))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txt_namcongtac, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel4))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txt_luongcanban, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel5))
                .addGap(6, 6, 6)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txt_luonghopdong, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel6))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel7)
                    .addComponent(rb_nganhan)
                    .addComponent(rb_daihan))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(btn_them)
                    .addComponent(btn_huy))
                .addContainerGap(12, Short.MAX_VALUE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void btn_huyActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btn_huyActionPerformed
        this.dispose();
    }//GEN-LAST:event_btn_huyActionPerformed

    private void rb_nvbcActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_rb_nvbcActionPerformed
        txt_luonghopdong.setEnabled(false);
        rb_nganhan.setEnabled(false);
        rb_daihan.setEnabled(false);
        txt_hesoluong.setEnabled(true);
        txt_namcongtac.setEnabled(true);
        txt_luongcanban.setEnabled(true);
    }//GEN-LAST:event_rb_nvbcActionPerformed

    private void rb_nvhdActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_rb_nvhdActionPerformed
        txt_luonghopdong.setEnabled(true);
        rb_nganhan.setEnabled(true);
        rb_daihan.setEnabled(true);
        txt_hesoluong.setEnabled(false);
        txt_namcongtac.setEnabled(false);
        txt_luongcanban.setEnabled(false);
    }//GEN-LAST:event_rb_nvhdActionPerformed

    private void btn_themActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btn_themActionPerformed
        String name = txt_name.getText().trim();
        String loainv = null;
        double hesoluong = 1.0;
        int sonamcongtac = 0;
        double luongcoban = 1800000.0;
        double luonghopdong = 0.0;
        String loaihopdong = null;

        if (rb_nvbc.isSelected()) {
            loainv = "NVBC";
            if (!txt_hesoluong.getText().trim().isEmpty()) {
                hesoluong = Double.parseDouble(txt_hesoluong.getText().trim());
            }
            if (!txt_luongcanban.getText().trim().isEmpty()) {
                luongcoban = Double.parseDouble(txt_luongcanban.getText().trim());
            }
            if (!txt_namcongtac.getText().trim().isEmpty()) {
                sonamcongtac = Integer.parseInt(txt_namcongtac.getText().trim());
            }

        } else if (rb_nvhd.isSelected()) {
            loainv = "NVHD";
            if (!txt_luonghopdong.getText().trim().isEmpty()) {
                luonghopdong = Double.parseDouble(txt_luonghopdong.getText().trim());
            }
            if (rb_daihan.isSelected()) {
                loaihopdong = "DH";
            } else {
                loaihopdong = "NH";
            }
        }

        String sql = "INSERT INTO nhanvien(hoten, loaiNV, hesoluong, sonamcongtac, luongcoban, luonghopdong, loaihopdong) VALUES (?, ?, ?, ?, ?, ?, ?)";
    
        try {
        DBAccess acc = new DBAccess();
        int rs = acc.UpdateWithParams(sql, name, loainv, hesoluong, sonamcongtac, luongcoban, luonghopdong, loaihopdong);
        
        if (rs > 0) {
            JOptionPane.showMessageDialog(null, "Add staff Success");
            // Clear form sau khi thêm thành công
            txt_name.setText("");
            txt_hesoluong.setText("");
            txt_namcongtac.setText("");
            txt_luongcanban.setText("");
            txt_luonghopdong.setText("");
        } else {
            JOptionPane.showMessageDialog(null, "Add staff Fail");
        }
    } catch (Exception e) {
        JOptionPane.showMessageDialog(null, "Error: " + e.getMessage());
    }
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(frm_Create.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(frm_Create.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(frm_Create.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(frm_Create.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new frm_Create().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btn_huy;
    private javax.swing.JButton btn_them;
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.ButtonGroup buttonGroup2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JLabel jLabel7;
    private javax.swing.JCheckBox rb_daihan;
    private javax.swing.JCheckBox rb_nganhan;
    private javax.swing.JCheckBox rb_nvbc;
    private javax.swing.JCheckBox rb_nvhd;
    private javax.swing.JTextField txt_hesoluong;
    private javax.swing.JTextField txt_luongcanban;
    private javax.swing.JTextField txt_luonghopdong;
    private javax.swing.JTextField txt_namcongtac;
    private javax.swing.JTextField txt_name;
    // End of variables declaration//GEN-END:variables
}

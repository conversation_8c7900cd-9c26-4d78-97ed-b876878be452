/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package bai_02_2;

import java.sql.*;
import javax.swing.*;

/**
 *
 * <AUTHOR>
 */
public class MyConnection {
    private static final String DRIVER = "com.mysql.cj.jdbc.Driver";
    private static final String URL = "*************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "";

    public Connection getConnection(){
        try{
            Class.forName(DRIVER);
            Connection con = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            return con;
        }catch(ClassNotFoundException ex){
            JOptionPane.showMessageDialog(null, "MySQL Driver not found: " + ex.getMessage(),
                "Driver Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }catch(SQLException ex){
            JOptionPane.showMessageDialog(null, "Database connection failed: " + ex.getMessage(),
                "Connection Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }catch(Exception ex){
            JOptionPane.showMessageDialog(null, "Unexpected error: " + ex.getMessage(),
                "Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }
    }
}

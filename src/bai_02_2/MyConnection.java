/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package bai_02_2;

import java.sql.*;
import javax.swing.*;

/**
 *
 * <AUTHOR>
 */
public class MyConnection {
    public Connection getConnection(){
        try{
            Class.forName("com.mysql.jdbc.Driver");
            String URL = "**************************************************=";
            Connection con = DriverManager.getConnection(URL);
            return con;
        }catch(Exception ex){
            JOptionPane.showMessageDialog(null, ex.toString(),"ERROR: Fail Conncection!", JOptionPane.ERROR_MESSAGE);
            return null;
        }           
    }        
}

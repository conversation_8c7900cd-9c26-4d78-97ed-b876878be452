/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JFrame.java to edit this template
 */
package bai_02_2;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

/**
 *
 * <AUTHOR>
 */
public class frm_Query extends javax.swing.JFrame {

    private DefaultTableModel tableModel;
    private DecimalFormat currencyFormat;

    /**
     * Creates new form frm_Query
     */
    public frm_Query() {
        initComponents();
        initializeForm();
    }

    /**
     * Initialize form components and setup
     */
    private void initializeForm() {
        // Initialize currency formatter
        currencyFormat = new DecimalFormat("#,###");

        // Setup table model
        setupTable();

        // Add event listener to button
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnDuyetActionPerformed(evt);
            }
        });
    }

    /**
     * Setup table with proper column headers and model
     */
    private void setupTable() {
        String[] columnNames = {
            "Họ Tên", "Loại NV", "Hệ Số Lương", "Số Năm Công Tác",
            "Lương Cơ Bản", "Lương Hợp Đồng", "Loại Hợp Đồng", "Lương"
        };

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make all cells non-editable
            }
        };

        jTable1.setModel(tableModel);

        // Set column widths
        jTable1.getColumnModel().getColumn(0).setPreferredWidth(120); // Họ Tên
        jTable1.getColumnModel().getColumn(1).setPreferredWidth(80);  // Loại NV
        jTable1.getColumnModel().getColumn(2).setPreferredWidth(100); // Hệ Số Lương
        jTable1.getColumnModel().getColumn(3).setPreferredWidth(120); // Số Năm Công Tác
        jTable1.getColumnModel().getColumn(4).setPreferredWidth(120); // Lương Cơ Bản
        jTable1.getColumnModel().getColumn(5).setPreferredWidth(120); // Lương Hợp Đồng
        jTable1.getColumnModel().getColumn(6).setPreferredWidth(120); // Loại Hợp Đồng
        jTable1.getColumnModel().getColumn(7).setPreferredWidth(120); // Lương
    }

    /**
     * Event handler for "Duyệt" button
     */
    private void btnDuyetActionPerformed(java.awt.event.ActionEvent evt) {
        String selectedType = (String) jComboBox1.getSelectedItem();
        loadEmployeeData(selectedType);
    }

    /**
     * Load employee data based on selected type
     * @param employeeType "Toàn bộ", "NVBC", or "NVHD"
     */
    private void loadEmployeeData(String employeeType) {
        DBAccess dbAccess = new DBAccess();

        try {
            if (!dbAccess.isConnected()) {
                JOptionPane.showMessageDialog(this, "Không thể kết nối đến cơ sở dữ liệu!",
                    "Lỗi kết nối", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Clear existing data
            tableModel.setRowCount(0);

            // Build SQL query based on selection
            String sql = "SELECT hoten, loaiNV, hesoluong, sonamcongtac, luongcoban, luonghopdong, loaihopdong FROM nhanvien";

            if (!"Toàn bộ".equals(employeeType)) {
                sql += " WHERE loaiNV = ?";
            }

            ResultSet rs;
            if (!"Toàn bộ".equals(employeeType)) {
                rs = dbAccess.QueryWithParams(sql, employeeType);
            } else {
                rs = dbAccess.Query(sql);
            }

            if (rs != null) {
                while (rs.next()) {
                    String hoten = rs.getString("hoten");
                    String loaiNV = rs.getString("loaiNV");
                    double hesoluong = rs.getDouble("hesoluong");
                    int sonamcongtac = rs.getInt("sonamcongtac");
                    double luongcoban = rs.getDouble("luongcoban");
                    double luonghopdong = rs.getDouble("luonghopdong");
                    String loaihopdong = rs.getString("loaihopdong");

                    // Calculate salary
                    double luong = calculateSalary(loaiNV, hesoluong, sonamcongtac, luongcoban, luonghopdong, loaihopdong);

                    // Add row to table
                    Object[] rowData = {
                        hoten,
                        loaiNV,
                        hesoluong > 0 ? String.valueOf(hesoluong) : "",
                        sonamcongtac > 0 ? String.valueOf(sonamcongtac) : "",
                        luongcoban > 0 ? currencyFormat.format(luongcoban) : "",
                        luonghopdong > 0 ? currencyFormat.format(luonghopdong) : "",
                        loaihopdong != null ? loaihopdong : "",
                        currencyFormat.format(luong)
                    };

                    tableModel.addRow(rowData);
                }
                rs.close();

                JOptionPane.showMessageDialog(this, "Đã tải " + tableModel.getRowCount() + " nhân viên",
                    "Thành công", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this, "Không có dữ liệu để hiển thị!",
                    "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Lỗi truy vấn dữ liệu: " + e.getMessage(),
                "Lỗi SQL", JOptionPane.ERROR_MESSAGE);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Lỗi: " + e.getMessage(),
                "Lỗi hệ thống", JOptionPane.ERROR_MESSAGE);
        } finally {
            dbAccess.closeConnection();
        }
    }

    /**
     * Calculate salary based on employee type and parameters
     * @param loaiNV Employee type (NVBC or NVHD)
     * @param hesoluong Salary coefficient
     * @param sonamcongtac Years of service
     * @param luongcoban Basic salary
     * @param luonghopdong Contract salary
     * @param loaihopdong Contract type (DH or NH)
     * @return Calculated salary
     */
    private double calculateSalary(String loaiNV, double hesoluong, int sonamcongtac,
                                 double luongcoban, double luonghopdong, String loaihopdong) {
        double luong = 0.0;

        if ("NVBC".equals(loaiNV)) {
            // NVBC: luongcanban + (hesoluong * sonamcongtac)
            luong = luongcoban + (hesoluong * sonamcongtac);
        } else if ("NVHD".equals(loaiNV)) {
            // NVHD: luonghopdong + thuongthem
            double thuongthem = 0.0;

            if ("DH".equals(loaihopdong)) {
                // Dài hạn: thưởng thêm 500,000
                thuongthem = 500000.0;
            } else if ("NH".equals(loaihopdong)) {
                // Ngắn hạn: thưởng thêm 1,000,000
                thuongthem = 1000000.0;
            }

            luong = luonghopdong + thuongthem;
        }

        return luong;
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jLabel1 = new javax.swing.JLabel();
        jButton1 = new javax.swing.JButton();
        jComboBox1 = new javax.swing.JComboBox<>();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jButton2 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        jLabel1.setFont(new java.awt.Font("Helvetica Neue", 1, 36)); // NOI18N
        jLabel1.setText("Danh Sách");

        jButton1.setText("Duyệt");

        jComboBox1.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Toàn bộ", "NVBC", "NVHD" }));

        jTable1.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null, null, null, null, null},
                {null, null, null, null, null, null, null, null},
                {null, null, null, null, null, null, null, null},
                {null, null, null, null, null, null, null, null}
            },
            new String [] {
                "Ho Ten", "Loai NV", "He so luong", "So nam cong tac", "Luong co ban", "Luong hop dong", "Loai hop dong", "Luong"
            }
        ) {
            boolean[] canEdit = new boolean [] {
                false, false, false, false, false, false, false, false
            };

            public boolean isCellEditable(int rowIndex, int columnIndex) {
                return canEdit [columnIndex];
            }
        });
        jScrollPane1.setViewportView(jTable1);

        jButton2.setText("Thoát");
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(6, 6, 6)
                        .addComponent(jLabel1)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                        .addComponent(jComboBox1, javax.swing.GroupLayout.PREFERRED_SIZE, 140, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(jButton1)
                        .addGap(4, 4, 4)
                        .addComponent(jButton2))
                    .addGroup(layout.createSequentialGroup()
                        .addContainerGap()
                        .addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 888, Short.MAX_VALUE)))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addContainerGap()
                        .addComponent(jLabel1))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(17, 17, 17)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(jButton1)
                            .addComponent(jComboBox1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(jButton2))))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 258, Short.MAX_VALUE)
                .addContainerGap())
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton2ActionPerformed
        this.dispose();
    }//GEN-LAST:event_jButton2ActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(frm_Query.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(frm_Query.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(frm_Query.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(frm_Query.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new frm_Query().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton2;
    private javax.swing.JComboBox<String> jComboBox1;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    // End of variables declaration//GEN-END:variables
}

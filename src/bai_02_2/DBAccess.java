/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package bai_02_2;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.JOptionPane;

/**
 *
 * <AUTHOR>
 */
public class DBAccess {
    private Connection con;
    private Statement stmt;
    private boolean isConnected;

    public DBAccess(){
        try {
            MyConnection mycon = new MyConnection();
            con = mycon.getConnection();
            if (con != null) {
                stmt = con.createStatement();
                isConnected = true;
            } else {
                isConnected = false;
                JOptionPane.showMessageDialog(null, "Failed to establish database connection!",
                    "Database Error", JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            isConnected = false;
            JOptionPane.showMessageDialog(null, "Database connection error: " + e.getMessage(),
                "Database Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Check if database connection is available
     * @return true if connected, false otherwise
     */
    public boolean isConnected() {
        return isConnected && con != null;
    }

    /**
     * Execute UPDATE, INSERT, DELETE statements without parameters
     * @param sql SQL statement
     * @return number of affected rows, -1 if error
     */
    public int Update(String sql){
        if (!isConnected()) {
            JOptionPane.showMessageDialog(null, "No database connection available!",
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return -1;
        }

        try {
            int result = stmt.executeUpdate(sql);
            return result;
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "SQL Error: " + e.getMessage(),
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return -1;
        }
    }

    /**
     * Execute UPDATE, INSERT, DELETE statements with parameters (safer)
     * @param sql SQL statement with ? placeholders
     * @param params Parameters to bind
     * @return number of affected rows, -1 if error
     */
    public int UpdateWithParams(String sql, Object... params) {
        if (!isConnected()) {
            JOptionPane.showMessageDialog(null, "No database connection available!",
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return -1;
        }

        PreparedStatement pstmt = null;
        try {
            pstmt = con.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            int result = pstmt.executeUpdate();
            return result;
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "SQL Error: " + e.getMessage(),
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return -1;
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    System.err.println("Error closing PreparedStatement: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Execute SELECT statements without parameters
     * @param sql SQL SELECT statement
     * @return ResultSet or null if error
     */
    public ResultSet Query(String sql){
        if (!isConnected()) {
            JOptionPane.showMessageDialog(null, "No database connection available!",
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }

        try {
            ResultSet rs = stmt.executeQuery(sql);
            return rs;
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "SQL Query Error: " + e.getMessage(),
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }
    }

    /**
     * Execute SELECT statements with parameters (safer)
     * @param sql SQL SELECT statement with ? placeholders
     * @param params Parameters to bind
     * @return ResultSet or null if error
     */
    public ResultSet QueryWithParams(String sql, Object... params) {
        if (!isConnected()) {
            JOptionPane.showMessageDialog(null, "No database connection available!",
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }

        try {
            PreparedStatement pstmt = con.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            ResultSet rs = pstmt.executeQuery();
            return rs;
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(null, "SQL Query Error: " + e.getMessage(),
                "Database Error", JOptionPane.ERROR_MESSAGE);
            return null;
        }
    }

    /**
     * Close database connection and resources
     */
    public void closeConnection() {
        try {
            if (stmt != null && !stmt.isClosed()) {
                stmt.close();
            }
            if (con != null && !con.isClosed()) {
                con.close();
            }
            isConnected = false;
        } catch (SQLException e) {
            System.err.println("Error closing database connection: " + e.getMessage());
        }
    }

    /**
     * Finalize method to ensure resources are cleaned up
     */
    @Override
    protected void finalize() throws Throwable {
        closeConnection();
        super.finalize();
    }
}
